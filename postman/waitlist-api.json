{"info": {"name": "Waitlist API", "description": "API endpoints for managing appointment waitlists", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "apikey", "apikey": [{"key": "key", "value": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}, {"key": "value", "value": "BLOOKD", "type": "string"}]}, "variable": [{"key": "baseUrl", "value": "{{baseUrl}}/api/v2/waitlist", "type": "string"}, {"key": "token", "value": "{{token}}", "type": "string"}], "item": [{"name": "Join <PERSON>", "request": {"method": "POST", "header": [{"key": "<PERSON><PERSON><PERSON><PERSON>", "value": "BLOOKD", "type": "text"}, {"key": "token", "value": "{{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"serviceProviderId\": 123,\n  \"businessLocationId\": 456,\n  \"userId\": 789,\n  \"customerId\": 789,\n  \"preferredDate\": \"2024-01-15\",\n  \"preferredTimeStart\": \"14:00\",\n  \"preferredTimeEnd\": \"16:00\",\n  \"totalDuration\": 90,\n  \"services\": [\n    {\n      \"serviceId\": 101\n    }\n  ],\n  \"products\": [\n    {\n      \"productVariantId\": 201,\n      \"quantity\": 2\n    }\n  ],\n  \"expirationDays\": 30\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/join", "host": ["{{baseUrl}}"], "path": ["join"]}, "description": "Join a waitlist when no appointment slots are available"}, "response": []}, {"name": "Get User Waitlist Entries", "request": {"method": "GET", "header": [{"key": "<PERSON><PERSON><PERSON><PERSON>", "value": "BLOOKD", "type": "text"}, {"key": "token", "value": "{{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/user/789?status=Active&includeExpired=false", "host": ["{{baseUrl}}"], "path": ["user", "789"], "query": [{"key": "status", "value": "Active", "description": "Filter by waitlist status (Active, Notified, Converted, Expired, Cancelled)"}, {"key": "includeExpired", "value": "false", "description": "Include expired entries (default: false)"}]}, "description": "Get all waitlist entries for a specific user"}, "response": []}, {"name": "Get Waitlist Entry by ID", "request": {"method": "GET", "header": [{"key": "<PERSON><PERSON><PERSON><PERSON>", "value": "BLOOKD", "type": "text"}, {"key": "token", "value": "{{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/{{waitlistId}}", "host": ["{{baseUrl}}"], "path": ["{{waitlistId}}"], "variable": [{"key": "waitlistId", "value": "uuid-here", "description": "Waitlist entry UUID"}]}, "description": "Get a specific waitlist entry with full details"}, "response": []}, {"name": "Convert Waitlist to Booking", "request": {"method": "POST", "header": [{"key": "<PERSON><PERSON><PERSON><PERSON>", "value": "BLOOKD", "type": "text"}, {"key": "token", "value": "{{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"date\": \"2024-01-15\",\n  \"timeSlot\": \"14:00\",\n  \"totalAmount\": \"75.00\",\n  \"remainingAmount\": \"75.00\",\n  \"subTotal\": \"70.00\",\n  \"tax\": 5.00,\n  \"paymentFrom\": \"Card\",\n  \"walletAmount\": 0,\n  \"promocode\": \"\",\n  \"discount\": \"0\",\n  \"description\": \"Converted from waitlist\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/convert/{{waitlistId}}", "host": ["{{baseUrl}}"], "path": ["convert", "{{waitlistId}}"], "variable": [{"key": "waitlistId", "value": "uuid-here", "description": "Waitlist entry UUID to convert"}]}, "description": "Convert a waitlist entry to an actual booking"}, "response": []}, {"name": "<PERSON><PERSON><PERSON> from Waitlist", "request": {"method": "DELETE", "header": [{"key": "<PERSON><PERSON><PERSON><PERSON>", "value": "BLOOKD", "type": "text"}, {"key": "token", "value": "{{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/{{waitlistId}}", "host": ["{{baseUrl}}"], "path": ["{{waitlistId}}"], "variable": [{"key": "waitlistId", "value": "uuid-here", "description": "Waitlist entry UUID to remove"}]}, "description": "Remove/cancel a waitlist entry"}, "response": []}, {"name": "Get Provider Waitlist Entries", "request": {"method": "GET", "header": [{"key": "<PERSON><PERSON><PERSON><PERSON>", "value": "BLOOKD", "type": "text"}, {"key": "token", "value": "{{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/provider/123?status=Active&businessLocationId=456&date=2024-01-15&page=1&limit=20", "host": ["{{baseUrl}}"], "path": ["provider", "123"], "query": [{"key": "status", "value": "Active", "description": "Filter by waitlist status"}, {"key": "businessLocationId", "value": "456", "description": "Filter by business location"}, {"key": "date", "value": "2024-01-15", "description": "Filter by preferred date"}, {"key": "page", "value": "1", "description": "Page number (default: 1)"}, {"key": "limit", "value": "20", "description": "Items per page (default: 20)"}]}, "description": "Get waitlist entries for a specific service provider"}, "response": []}]}