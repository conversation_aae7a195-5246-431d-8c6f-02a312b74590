{"info": {"name": "Waitlists", "description": "API endpoints for managing appointment waitlists", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "apikey", "apikey": [{"key": "key", "value": "api-key", "type": "string"}, {"key": "value", "value": "BLOOKD", "type": "string"}]}, "variable": [{"key": "base_url", "value": "{{base_url}}/api/v2/waitlist", "type": "string"}, {"key": "token", "value": "{{token}}", "type": "string"}], "item": [{"name": "Join <PERSON>", "request": {"method": "POST", "header": [{"key": "api-key", "value": "BLOOKD", "type": "text"}, {"key": "token", "value": "{{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"serviceProviderId\": 123,\n  \"businessLocationId\": 456,\n  \"preferredDate\": \"2024-01-15\",\n  \"preferredTimeStart\": \"14:00\",\n  \"preferredTimeEnd\": \"16:00\",\n  \"totalDuration\": 90,\n  \"services\": [\n    {\n      \"serviceId\": 101\n    }\n  ],\n  \"products\": [\n    {\n      \"productVariantId\": 201,\n      \"quantity\": 2\n    }\n  ],\n  \"expirationDays\": 30\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/join", "host": ["{{base_url}}"], "path": ["join"]}, "description": "Join a waitlist when no appointment slots are available"}, "response": []}, {"name": "Get Waitlist Entries", "request": {"method": "GET", "header": [{"key": "api-key", "value": "BLOOKD", "type": "text"}, {"key": "token", "value": "{{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/?status=Active&includeExpired=false&page=1&limit=20", "host": ["{{base_url}}"], "path": [""], "query": [{"key": "status", "value": "Active", "description": "Filter by waitlist status (Active, Notified, Converted, Expired, Cancelled)"}, {"key": "includeExpired", "value": "false", "description": "Include expired entries (default: false)"}, {"key": "page", "value": "1", "description": "Page number (default: 1)"}, {"key": "limit", "value": "20", "description": "Items per page (default: 20)"}]}, "description": "Get waitlist entries (users see their own, service providers see their services)"}, "response": []}, {"name": "Get Waitlist Entry by ID", "request": {"method": "GET", "header": [{"key": "api-key", "value": "BLOOKD", "type": "text"}, {"key": "token", "value": "{{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/{{waitlistId}}", "host": ["{{base_url}}"], "path": ["{{waitlistId}}"], "variable": [{"key": "waitlistId", "value": "uuid-here", "description": "Waitlist entry UUID"}]}, "description": "Get a specific waitlist entry with full details"}, "response": []}, {"name": "Convert Waitlist to Booking", "request": {"method": "POST", "header": [{"key": "api-key", "value": "BLOOKD", "type": "text"}, {"key": "token", "value": "{{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"date\": \"2024-01-15\",\n  \"timeSlot\": \"14:00\",\n  \"totalAmount\": \"75.00\",\n  \"remainingAmount\": \"75.00\",\n  \"subTotal\": \"70.00\",\n  \"tax\": 5.00,\n  \"paymentFrom\": \"Card\",\n  \"walletAmount\": 0,\n  \"promocode\": \"\",\n  \"discount\": \"0\",\n  \"description\": \"Converted from waitlist\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/convert/{{waitlistId}}", "host": ["{{base_url}}"], "path": ["convert", "{{waitlistId}}"], "variable": [{"key": "waitlistId", "value": "uuid-here", "description": "Waitlist entry UUID to convert"}]}, "description": "Convert a waitlist entry to an actual booking"}, "response": []}, {"name": "<PERSON><PERSON><PERSON> from Waitlist", "request": {"method": "DELETE", "header": [{"key": "api-key", "value": "BLOOKD", "type": "text"}, {"key": "token", "value": "{{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/{{waitlistId}}", "host": ["{{base_url}}"], "path": ["{{waitlistId}}"], "variable": [{"key": "waitlistId", "value": "uuid-here", "description": "Waitlist entry UUID to remove"}]}, "description": "Remove/cancel a waitlist entry"}, "response": []}]}