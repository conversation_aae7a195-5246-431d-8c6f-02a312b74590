-- Create waitlist table
CREATE TABLE IF NOT EXISTS `tbl_appointment_waitlist` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `waitlist_id` VARCHAR(128) NOT NULL UNIQUE,
  `service_provider_id` INT NOT NULL,
  `business_location_id` INT NOT NULL,
  `customer_id` INT NOT NULL,
  `user_id` INT NOT NULL,
  `customer_name` VARCHAR(256) NOT NULL,
  `preferred_date` DATE NOT NULL,
  `preferred_time_start` VARCHAR(5) NULL, -- HH:mm format
  `preferred_time_end` VARCHAR(5) NULL,   -- HH:mm format
  `total_duration` DOUBLE(8,2) NOT NULL,
  `services` JSON NOT NULL, -- Store service details as JSON
  `products` JSON DEFAULT NULL, -- Store product details as JSON
  `waitlist_status` ENUM('Active', 'Notified', 'Converted', 'Expired', 'Cancelled') DEFAULT 'Active',
  `notification_sent_at` DATETIME NULL,
  `expires_at` DATETIME NOT NULL, -- When the waitlist entry expires
  `priority_score` INT DEFAULT 0, -- For prioritizing waitlist entries
  `converted_booking_id` VARCHAR(128) NULL, -- References tbl_appointment_booking.booking_id
  `converted_at` DATETIME NULL,
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_deleted` TINYINT(1) DEFAULT 0,
  
  PRIMARY KEY (`id`),
  UNIQUE INDEX `waitlist_id_UNIQUE` (`waitlist_id` ASC),
  INDEX `idx_sp_location_date` (`service_provider_id`, `business_location_id`, `preferred_date`),
  INDEX `idx_user_status` (`user_id`, `waitlist_status`),
  INDEX `idx_status_expires` (`waitlist_status`, `expires_at`),
  INDEX `idx_converted_booking` (`converted_booking_id`)
);

-- Add waitlist_id column to booking table
ALTER TABLE `tbl_appointment_booking` 
ADD COLUMN `waitlist_id` VARCHAR(128) NULL AFTER `booking_id`,
ADD INDEX `idx_waitlist_id` (`waitlist_id`);
