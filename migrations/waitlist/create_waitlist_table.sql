CREATE TABLE IF NOT EXISTS appointment_waitlist (
  id VARCHAR(36) NOT NULL DEFAULT (UUID()),
  serviceProviderId INT NOT NULL,
  businessLocationId INT NOT NULL,
  userId VARCHAR(36) NOT NULL,
  preferredDate DATE NOT NULL,
  preferredTimeStart VARCHAR(5) NULL,
  preferredTimeEnd VARCHAR(5) NULL,
  totalDuration DOUBLE(8,2) NOT NULL,
  waitlistStatus ENUM('Active', 'Notified', 'Converted', 'Expired', 'Cancelled') DEFAULT 'Active',
  notificationSentAt DATETIME NULL,
  expiresAt DATETIME NOT NULL,
  priorityScore INT DEFAULT 0,
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  isDeleted TINYINT(1) DEFAULT 0,

  PRIMARY KEY (id),
  INDEX idx_sp_location_date (serviceProviderId, businessLocationId, preferredDate),
  INDEX idx_user_status (userId, waitlistStatus),
  INDEX idx_status_expires (waitlistStatus, expiresAt)
);

CREATE TABLE IF NOT EXISTS waitlist_services (
  id VARCHAR(36) NOT NULL DEFAULT (UUID()),
  waitlistId VARCHAR(36) NOT NULL,
  serviceId INT NOT NULL,
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  isDeleted TINYINT(1) DEFAULT 0,

  PRIMARY KEY (id),
  INDEX idx_waitlist_id (waitlistId),
  INDEX idx_service_id (serviceId),
  FOREIGN KEY (waitlistId) REFERENCES appointment_waitlist(id) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS waitlist_products (
  id VARCHAR(36) NOT NULL DEFAULT (UUID()),
  waitlistId VARCHAR(36) NOT NULL,
  productVariantId INT NOT NULL,
  quantity INT NOT NULL,
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  isDeleted TINYINT(1) DEFAULT 0,

  PRIMARY KEY (id),
  INDEX idx_waitlist_id (waitlistId),
  INDEX idx_product_variant_id (productVariantId),
  FOREIGN KEY (waitlistId) REFERENCES appointment_waitlist(id) ON DELETE CASCADE
);

ALTER TABLE tbl_appointment_booking
ADD COLUMN waitlistId VARCHAR(36) NULL AFTER booking_id,
ADD INDEX idx_waitlist_id (waitlistId);
