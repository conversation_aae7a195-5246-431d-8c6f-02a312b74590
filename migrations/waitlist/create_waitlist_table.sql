-- Create waitlist table with <PERSON><PERSON><PERSON> primary key
CREATE TABLE IF NOT EXISTS `tbl_appointment_waitlist` (
  `id` VARCHAR(36) NOT NULL, -- UUID primary key
  `serviceProviderId` INT NOT NULL,
  `businessLocationId` INT NOT NULL,
  `customerId` INT NOT NULL,
  `userId` INT NOT NULL,
  `preferredDate` DATE NOT NULL,
  `preferredTimeStart` VARCHAR(5) NULL, -- HH:mm format
  `preferredTimeEnd` VARCHAR(5) NULL,   -- HH:mm format
  `totalDuration` DOUBLE(8,2) NOT NULL,
  `waitlistStatus` ENUM('Active', 'Notified', 'Converted', 'Expired', 'Cancelled') DEFAULT 'Active',
  `notificationSentAt` DATETIME NULL,
  `expiresAt` DATETIME NOT NULL, -- When the waitlist entry expires
  `priorityScore` INT DEFAULT 0, -- For prioritizing waitlist entries
  `createdAt` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `isDeleted` TINYINT(1) DEFAULT 0,

  PRIMARY KEY (`id`),
  INDEX `idx_sp_location_date` (`serviceProviderId`, `businessLocationId`, `preferredDate`),
  INDEX `idx_user_status` (`userId`, `waitlistStatus`),
  INDEX `idx_status_expires` (`waitlistStatus`, `expiresAt`)
);

-- Create waitlist services join table (only store IDs and quantity info)
CREATE TABLE IF NOT EXISTS `tbl_waitlist_services` (
  `id` VARCHAR(36) NOT NULL, -- UUID primary key
  `waitlistId` VARCHAR(36) NOT NULL,
  `serviceId` INT NOT NULL,
  `duration` INT NOT NULL, -- Keep duration as it affects scheduling
  `createdAt` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `isDeleted` TINYINT(1) DEFAULT 0,

  PRIMARY KEY (`id`),
  INDEX `idx_waitlist_id` (`waitlistId`),
  INDEX `idx_service_id` (`serviceId`),
  FOREIGN KEY (`waitlistId`) REFERENCES `tbl_appointment_waitlist`(`id`) ON DELETE CASCADE
);

-- Create waitlist products join table (only store IDs and quantity info)
CREATE TABLE IF NOT EXISTS `tbl_waitlist_products` (
  `id` VARCHAR(36) NOT NULL, -- UUID primary key
  `waitlistId` VARCHAR(36) NOT NULL,
  `productId` INT NOT NULL,
  `subproductId` INT NOT NULL DEFAULT 0,
  `quantity` INT NOT NULL,
  `createdAt` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `isDeleted` TINYINT(1) DEFAULT 0,

  PRIMARY KEY (`id`),
  INDEX `idx_waitlist_id` (`waitlistId`),
  INDEX `idx_product_id` (`productId`),
  FOREIGN KEY (`waitlistId`) REFERENCES `tbl_appointment_waitlist`(`id`) ON DELETE CASCADE
);

-- Add waitlistId column to booking table (references UUID)
ALTER TABLE `tbl_appointment_booking`
ADD COLUMN `waitlistId` VARCHAR(36) NULL AFTER `booking_id`,
ADD INDEX `idx_waitlist_id` (`waitlistId`);
