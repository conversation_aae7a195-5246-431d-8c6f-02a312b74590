import 'module-alias/register';
import Router from 'express-promise-router';
import Health from './health';
import PaymentRoutes from './payments';
import UserRoutes from './user/routes';
import ServiceProviderRoutes from './service_provider/routes';
import ChatRoutes from './chat';
import BookingRoutes from './booking/routes';
import Views from './documents/routes';
import ProductRoutes from './product/routes';
import { WebhooksRouter } from './webhooks';
import ServiceCategoriesRouter from './service_categories/routes';
import { EmailRouter } from './email'
import { FilesRouter } from './files'
import { NotificationsRouter } from './common/routes'
import ImagesRouter from './images'
import BlockListRoutes from './blockLists'
import CommentsRouter from './comments'
import WaitlistRouter from './waitlist'

const router = Router();

router.use(Health)
router.use(PaymentRoutes)
router.use(UserRoutes)
router.use(ServiceProviderRoutes)
router.use(ChatRoutes)
router.use(BookingRoutes)
router.use(Views)
router.use(ProductRoutes)
router.use(WebhooksRouter)
router.use(ServiceCategoriesRouter)
router.use(EmailRouter)
router.use(FilesRouter)
router.use(NotificationsRouter)
router.use(ImagesRouter)
router.use('/block-lists', BlockListRoutes)
router.use('/comments', CommentsRouter)
router.use('/waitlists', WaitlistRouter)
export default router
