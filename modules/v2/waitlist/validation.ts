import Joi from 'joi'

export const joinWaitlistSchema = Joi.object({
  serviceProviderId: Joi.number().integer().positive().required(),
  businessLocationId: Joi.number().integer().positive().required(),
  preferredDate: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/).required(),
  preferredTimeStart: Joi.string().pattern(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/).optional(),
  preferredTimeEnd: Joi.string().pattern(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/).optional(),
  totalDuration: Joi.number().positive().required(),
  services: Joi.array().items(
    Joi.object({
      serviceId: Joi.number().integer().positive().required()
    })
  ).min(1).required(),
  products: Joi.array().items(
    Joi.object({
      productVariantId: Joi.number().integer().positive().required(),
      quantity: Joi.number().integer().positive().required()
    })
  ).optional(),
  expirationDays: Joi.number().integer().positive().max(365).default(30).optional()
})

export const convertWaitlistSchema = Joi.object({
  date: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/).required(),
  timeSlot: Joi.string().pattern(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/).required(),
  totalAmount: Joi.string().pattern(/^\d+(\.\d{1,2})?$/).required(),
  remainingAmount: Joi.string().pattern(/^\d+(\.\d{1,2})?$/).required(),
  subTotal: Joi.string().pattern(/^\d+(\.\d{1,2})?$/).required(),
  tax: Joi.number().min(0).required(),
  paymentFrom: Joi.string().valid('Card', 'Cash', 'Wallet', 'Online').required(),
  walletAmount: Joi.number().min(0).default(0).optional(),
  promocode: Joi.string().allow('').optional(),
  discount: Joi.string().pattern(/^\d+(\.\d{1,2})?$/).default('0').optional(),
  description: Joi.string().max(500).optional()
})

export const getWaitlistEntriesSchema = Joi.object({
  status: Joi.string().valid('Active', 'Notified', 'Converted', 'Expired', 'Cancelled').optional(),
  includeExpired: Joi.string().valid('true', 'false').default('false').optional(),
  businessLocationId: Joi.number().integer().positive().optional(),
  date: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/).optional(),
  page: Joi.number().integer().positive().default(1).optional(),
  limit: Joi.number().integer().positive().max(100).default(20).optional()
})

export const waitlistIdSchema = Joi.object({
  waitlistId: Joi.string().uuid().required()
})
