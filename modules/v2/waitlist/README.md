# Waitlist API Documentation

## Overview
The Waitlist API allows customers to join a waitlist when their preferred appointment slots are not available, and provides functionality to manage and convert waitlist entries to actual bookings.

## Base URL
```
/api/v2/waitlist
```

## Authentication
All endpoints require authentication headers:
- `apiKey: BLOOKD`
- `token: {{token}}`

## Endpoints

### 1. Join Waitlist
**POST** `/join`

Join a waitlist when no appointment slots are available.

**Request Body:**
```json
{
  "serviceProviderId": 123,
  "businessLocationId": 456,
  "userId": 789,
  "customerId": 789,
  "preferredDate": "2024-01-15",
  "preferredTimeStart": "14:00",
  "preferredTimeEnd": "16:00",
  "totalDuration": 90,
  "services": [
    {
      "serviceId": 101
    }
  ],
  "products": [
    {
      "productVariantId": 201,
      "quantity": 2
    }
  ],
  
  // Optional: Extended time flexibility
  "flexibleDates": true,
  "dateRangeStart": "2024-01-15",
  "dateRangeEnd": "2024-01-30",
  "acceptableTimeSlots": ["09:00", "14:00", "16:30"],
  "acceptableDaysOfWeek": [1, 2, 3, 4, 5],
  "morningOnly": true,
  "expirationDays": 30
}
```

**Response:**
```json
{
  "message": "Successfully joined waitlist",
  "data": {
    "id": "uuid-here",
    "serviceProviderId": 123,
    "preferredDate": "2024-01-15",
    "waitlistStatus": "Active",
    "services": [
      {
        "serviceId": 101,
        "name": "Hair Cut",
        "price": 50.00,
        "duration": 30
      }
    ],
    "products": [
      {
        "productVariantId": 201,
        "quantity": 2,
        "name": "Hair Shampoo",
        "price": 25.00,
        "size": 500,
        "unit": "ml"
      }
    ],
    "customerName": "John Doe",
    "serviceProviderName": "Jane Smith"
  }
}
```

### 2. Get User's Waitlist Entries
**GET** `/user/:userId`

Get all waitlist entries for a specific user.

**Query Parameters:**
- `status` (optional): Filter by waitlist status (Active, Notified, Converted, Expired, Cancelled)
- `includeExpired` (optional): Include expired entries (default: false)

**Response:**
```json
{
  "message": "Waitlist entries retrieved successfully",
  "data": [
    {
      "id": "uuid-here",
      "serviceProviderId": 123,
      "preferredDate": "2024-01-15",
      "waitlistStatus": "Active",
      "services": [...],
      "products": [...]
    }
  ]
}
```

### 3. Get Waitlist Entry by ID
**GET** `/:waitlistId`

Get a specific waitlist entry with full details.

**Response:**
```json
{
  "message": "Waitlist entry retrieved successfully",
  "data": {
    "id": "uuid-here",
    "serviceProviderId": 123,
    "waitlistStatus": "Active",
    // ... full waitlist details
  }
}
```

### 4. Convert Waitlist to Booking
**POST** `/convert/:waitlistId`

Convert a waitlist entry to an actual booking.

**Request Body:**
```json
{
  "date": "2024-01-15",
  "timeSlot": "14:00",
  "totalAmount": "75.00",
  "remainingAmount": "75.00",
  "subTotal": "70.00",
  "tax": 5.00,
  "paymentFrom": "Card"
}
```

**Response:**
```json
{
  "message": "Waitlist successfully converted to booking",
  "data": {
    "booking": {
      "booking_id": "BOOK-123",
      // ... booking details
    },
    "waitlistId": "uuid-here"
  }
}
```

### 5. Remove from Waitlist
**DELETE** `/:waitlistId`

Remove/cancel a waitlist entry.

**Response:**
```json
{
  "message": "Successfully removed from waitlist"
}
```

### 6. Get Provider's Waitlist Entries
**GET** `/provider/:serviceProviderId`

Get waitlist entries for a specific service provider.

**Query Parameters:**
- `status` (optional): Filter by waitlist status
- `businessLocationId` (optional): Filter by business location
- `date` (optional): Filter by preferred date
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 20)

**Response:**
```json
{
  "message": "Provider waitlist entries retrieved successfully",
  "data": [...],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 15
  }
}
```

## Waitlist Status Values
- `Active`: Customer is waiting for availability
- `Notified`: Customer has been notified of available slot
- `Converted`: Waitlist entry was converted to booking
- `Expired`: Waitlist entry has expired
- `Cancelled`: Customer cancelled their waitlist entry

## Time Flexibility Options

### Basic Time Preference
```json
{
  "preferredDate": "2024-01-15",
  "preferredTimeStart": "14:00",
  "preferredTimeEnd": "16:00"
}
```

### Flexible Date Range
```json
{
  "flexibleDates": true,
  "dateRangeStart": "2024-01-15",
  "dateRangeEnd": "2024-01-30",
  "morningOnly": true
}
```

### Specific Time Slots
```json
{
  "flexibleDates": true,
  "dateRangeStart": "2024-01-15",
  "dateRangeEnd": "2024-01-30",
  "acceptableTimeSlots": ["09:00", "14:00", "16:30"]
}
```

### Day of Week Preferences
```json
{
  "flexibleDates": true,
  "dateRangeStart": "2024-01-15",
  "dateRangeEnd": "2024-02-15",
  "acceptableDaysOfWeek": [1, 2, 3, 4, 5]  // Weekdays only
}
```

### Multiple Specific Options
```json
{
  "acceptableTimeFrames": [
    { "date": "2024-01-15", "timeStart": "09:00", "timeEnd": "11:00" },
    { "date": "2024-01-16", "timeStart": "14:00", "timeEnd": "16:00" },
    { "date": "2024-01-17", "timeStart": "10:00", "timeEnd": "12:00" }
  ]
}
```

## Error Responses

### 400 Bad Request
```json
{
  "message": "Missing required fields: serviceProviderId, businessLocationId, userId, preferredDate, services"
}
```

### 404 Not Found
```json
{
  "message": "Waitlist entry not found"
}
```

### 403 Forbidden
```json
{
  "message": "You can only view your own waitlist entries"
}
```

### 500 Internal Server Error
```json
{
  "message": "Internal server error"
}
```
