import { Request, Response } from 'express'
import Router from 'express-promise-router'
import {
  createWaitlistEntry,
  getWaitlistEntryById,
  getWaitlistEntries as getWaitlistEntriesFromService,
  updateWaitlistEntry,
  deleteWaitlistEntry,
  getWaitlistEntryWithDetails,
  getWaitlistEntriesWithDetails,
  WaitlistStatus,
  IJoinWaitlistParams
} from '@/src/service/waitlist'
import { createBooking, USER_TYPE } from '@/src/service'
import moment from 'moment'
import {
  validateCallerIsUser,
  validateCallerIsUserOrServiceProvider,
  validateJoinWaitlistBody,
  validateConvertWaitlistBody,
  validateWaitlistEntriesQuery,
  validateWaitlistIdParam
} from './middleware'

const WaitlistRouter = Router()

WaitlistRouter.post('/join', validateCallerIsUser, validateJoinWaitlistBody, joinWaitlist)
WaitlistRouter.get('/', validateCallerIsUserOrServiceProvider, validateWaitlistEntriesQuery, getWaitlistEntriesHandler)
WaitlistRouter.get('/:waitlistId', validateCallerIsUserOrServiceProvider, validateWaitlistIdParam, getWaitlistEntry)
WaitlistRouter.post('/convert/:waitlistId', validateCallerIsUser, validateWaitlistIdParam, validateConvertWaitlistBody, convertWaitlistToBooking)
WaitlistRouter.delete('/:waitlistId', validateCallerIsUser, validateWaitlistIdParam, removeFromWaitlist)

async function joinWaitlist(req: Request, res: Response) {
  const { user_id } = req

  const waitlistParams: IJoinWaitlistParams = {
    ...req.body,
    userId: Number(user_id)
  }

  const waitlistEntry = await createWaitlistEntry(waitlistParams)

  if (!waitlistEntry) {
    return res.status(500).json({
      message: 'Failed to create waitlist entry'
    })
  }

  const waitlistWithDetails = await getWaitlistEntryWithDetails(waitlistEntry.id!)

  res.status(201).json({
    message: 'Successfully joined waitlist',
    data: waitlistWithDetails
  })
}

async function getWaitlistEntriesHandler(req: Request, res: Response) {
  const { user_type, user_id } = req
  const query = req.query as any

  const filters: any = {
    isDeleted: false,
    limit: query.limit,
    offset: (query.page - 1) * query.limit
  }

  if (user_type === USER_TYPE.USER) {
    filters.userId = Number(user_id)
  } else if (user_type === USER_TYPE.SERVICE_PROVIDER) {
    filters.serviceProviderId = Number(user_id)
  }

  if (query.status) {
    filters.waitlistStatus = query.status as WaitlistStatus
  }

  if (query.businessLocationId) {
    filters.businessLocationId = query.businessLocationId
  }

  if (query.date) {
    filters.preferredDate = query.date
  }

  if (query.includeExpired === 'false') {
    filters.waitlistStatus = WaitlistStatus.ACTIVE
  }

  const waitlistEntries = await getWaitlistEntriesFromService(filters)
  const waitlistWithDetails = await getWaitlistEntriesWithDetails(waitlistEntries)

  res.json({
    message: 'Waitlist entries retrieved successfully',
    data: waitlistWithDetails,
    pagination: {
      page: query.page,
      limit: query.limit,
      total: waitlistWithDetails.length
    }
  })
}

async function getWaitlistEntry(req: Request, res: Response) {
  const { user_type, user_id } = req
  const { waitlistId } = req.params

  const waitlistEntry = await getWaitlistEntryWithDetails(waitlistId)

  if (!waitlistEntry) {
    return res.status(404).json({
      message: 'Waitlist entry not found'
    })
  }

  if (user_type === USER_TYPE.USER && waitlistEntry.userId !== Number(user_id)) {
    return res.status(403).json({
      message: 'You can only view your own waitlist entries'
    })
  }

  if (user_type === USER_TYPE.SERVICE_PROVIDER && waitlistEntry.serviceProviderId !== Number(user_id)) {
    return res.status(403).json({
      message: 'You can only view waitlist entries for your services'
    })
  }

  res.json({
    message: 'Waitlist entry retrieved successfully',
    data: waitlistEntry
  })
}

async function convertWaitlistToBooking(req: Request, res: Response) {
  const { user_id } = req
  const { waitlistId } = req.params

  const waitlistEntry = await getWaitlistEntryById(waitlistId)
    if (!waitlistEntry) {
      return res.status(404).json({
        message: 'Waitlist entry not found'
      })
    }

    if (waitlistEntry.userId !== Number(user_id)) {
      return res.status(403).json({
        message: 'You can only convert your own waitlist entries'
      })
    }

    if (waitlistEntry.waitlistStatus !== WaitlistStatus.NOTIFIED) {
      return res.status(400).json({
        message: 'Waitlist entry must be in Notified status to convert'
      })
    }

    const bookingServices = (waitlistEntry.services || []).map(ws => ({
      service_id: ws.serviceId,
      duration: 0,
      price: 0
    }))

    const bookingProducts = (waitlistEntry.products || []).map(wp => ({
      product_id: 0,
      subproduct_id: wp.productVariantId,
      quantity: wp.quantity,
      price: 0
    }))
  const bookingParams = {
    service_provider_id: waitlistEntry.serviceProviderId.toString(),
    business_location_id: waitlistEntry.businessLocationId.toString(),
    user_id: waitlistEntry.userId.toString(),
    date: req.body.date,
    time_slot: req.body.timeSlot,
    service: bookingServices,
    total_duration: waitlistEntry.totalDuration,
    additional_duration: 0,
    total_amount: req.body.totalAmount,
    remaining_amount: req.body.remainingAmount,
    sub_total: req.body.subTotal,
    payment_from: req.body.paymentFrom,
    tax: req.body.tax,
    wallet_amount: req.body.walletAmount,
    promocode: req.body.promocode,
    product: bookingProducts,
    description: req.body.description,
    discount: req.body.discount,
    waitlistId: waitlistEntry.id
  }

    const booking = await createBooking(bookingParams)

    if (!booking) {
      return res.status(500).json({
        message: 'Failed to create booking from waitlist'
      })
    }

    await updateWaitlistEntry(waitlistId, {
      waitlistStatus: WaitlistStatus.CONVERTED,
      convertedAt: moment().format('YYYY-MM-DD HH:mm:ss')
    })

  res.json({
    message: 'Waitlist successfully converted to booking',
    data: {
      booking,
      waitlistId
    }
  })
}

async function removeFromWaitlist(req: Request, res: Response) {
  const { user_id } = req
  const { waitlistId } = req.params

  const waitlistEntry = await getWaitlistEntryById(waitlistId)
  if (!waitlistEntry) {
    return res.status(404).json({
      message: 'Waitlist entry not found'
    })
  }

  if (waitlistEntry.userId !== Number(user_id)) {
    return res.status(403).json({
      message: 'You can only remove your own waitlist entries'
    })
  }

  const success = await deleteWaitlistEntry(waitlistId)

  if (!success) {
    return res.status(500).json({
      message: 'Failed to remove waitlist entry'
    })
  }

  res.json({
    message: 'Successfully removed from waitlist'
  })
}

export default WaitlistRouter
