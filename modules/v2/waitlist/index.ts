import { Request, Response } from 'express'
import Router from 'express-promise-router'
import { 
  createWaitlistEntry,
  getWaitlistEntryById,
  getWaitlistEntries,
  updateWaitlistEntry,
  deleteWaitlistEntry,
  getWaitlistEntryWithDetails,
  getWaitlistEntriesWithDetails,
  WaitlistStatus,
  IJoinWaitlistParams,
  IConvertWaitlistParams
} from '@/src/service/waitlist'
import { createBooking, USER_TYPE } from '@/src/service'
import { logger } from '@/src/utils'
import moment from 'moment'

const WaitlistRouter = Router()

// Join waitlist
WaitlistRouter.post('/join', joinWaitlist)

// Get user's waitlist entries
WaitlistRouter.get('/user/:userId', getUserWaitlistEntries)

// Get waitlist entry by ID
WaitlistRouter.get('/:waitlistId', getWaitlistEntry)

// Convert waitlist to booking
WaitlistRouter.post('/convert/:waitlistId', convertWaitlistToBooking)

// Cancel/remove from waitlist
WaitlistRouter.delete('/:waitlistId', removeFromWaitlist)

// Service provider endpoints
WaitlistRouter.get('/provider/:serviceProviderId', getProviderWaitlistEntries)

/**
 * Join waitlist when no slots are available
 */
async function joinWaitlist(req: Request, res: Response) {
  try {
    const {
      serviceProviderId,
      businessLocationId,
      customerId,
      userId,
      preferredDate,
      preferredTimeStart,
      preferredTimeEnd,
      totalDuration,
      services,
      products,
      // Extended time frame options
      flexibleDates,
      dateRangeStart,
      dateRangeEnd,
      acceptableTimeSlots,
      acceptableDaysOfWeek,
      morningOnly,
      afternoonOnly,
      eveningOnly,
      acceptableTimeFrames,
      expirationDays
    } = req.body

    // Validate required fields
    if (!serviceProviderId || !businessLocationId || !userId || !preferredDate || !services?.length) {
      return res.status(400).json({
        message: 'Missing required fields: serviceProviderId, businessLocationId, userId, preferredDate, services'
      })
    }

    const waitlistParams: IJoinWaitlistParams = {
      serviceProviderId: Number(serviceProviderId),
      businessLocationId: Number(businessLocationId),
      customerId: Number(customerId || userId),
      userId: Number(userId),
      preferredDate,
      preferredTimeStart,
      preferredTimeEnd,
      totalDuration: Number(totalDuration),
      services,
      products,
      flexibleDates,
      dateRangeStart,
      dateRangeEnd,
      acceptableTimeSlots,
      acceptableDaysOfWeek,
      morningOnly,
      afternoonOnly,
      eveningOnly,
      acceptableTimeFrames,
      expirationDays
    }

    const waitlistEntry = await createWaitlistEntry(waitlistParams)

    if (!waitlistEntry) {
      return res.status(500).json({
        message: 'Failed to create waitlist entry'
      })
    }

    // Get full details for response
    const waitlistWithDetails = await getWaitlistEntryWithDetails(waitlistEntry.id!)

    res.status(201).json({
      message: 'Successfully joined waitlist',
      data: waitlistWithDetails
    })

  } catch (error) {
    logger.error({ message: 'Failed to join waitlist', error, meta: req.body })
    res.status(500).json({
      message: 'Internal server error'
    })
  }
}

/**
 * Get user's waitlist entries
 */
async function getUserWaitlistEntries(req: Request, res: Response) {
  try {
    const { userId } = req.params
    const { status, includeExpired = 'false' } = req.query

    if (!userId) {
      return res.status(400).json({
        message: 'User ID is required'
      })
    }

    const filters: any = {
      userId: Number(userId),
      isDeleted: false
    }

    if (status) {
      filters.waitlistStatus = status as WaitlistStatus
    }

    if (includeExpired === 'false') {
      // Only get non-expired entries
      filters.waitlistStatus = WaitlistStatus.ACTIVE
    }

    const waitlistEntries = await getWaitlistEntries(filters)
    const waitlistWithDetails = await getWaitlistEntriesWithDetails(waitlistEntries)

    res.json({
      message: 'Waitlist entries retrieved successfully',
      data: waitlistWithDetails
    })

  } catch (error) {
    logger.error({ message: 'Failed to get user waitlist entries', error, meta: req.params })
    res.status(500).json({
      message: 'Internal server error'
    })
  }
}

/**
 * Get waitlist entry by ID
 */
async function getWaitlistEntry(req: Request, res: Response) {
  try {
    const { waitlistId } = req.params

    if (!waitlistId) {
      return res.status(400).json({
        message: 'Waitlist ID is required'
      })
    }

    const waitlistEntry = await getWaitlistEntryWithDetails(waitlistId)

    if (!waitlistEntry) {
      return res.status(404).json({
        message: 'Waitlist entry not found'
      })
    }

    res.json({
      message: 'Waitlist entry retrieved successfully',
      data: waitlistEntry
    })

  } catch (error) {
    logger.error({ message: 'Failed to get waitlist entry', error, meta: req.params })
    res.status(500).json({
      message: 'Internal server error'
    })
  }
}

/**
 * Convert waitlist entry to actual booking
 */
async function convertWaitlistToBooking(req: Request, res: Response) {
  try {
    const { waitlistId } = req.params
    const {
      date,
      timeSlot,
      totalAmount,
      remainingAmount,
      subTotal,
      paymentFrom,
      tax,
      walletAmount,
      promocode,
      discount,
      description
    } = req.body

    if (!waitlistId || !date || !timeSlot) {
      return res.status(400).json({
        message: 'Missing required fields: waitlistId, date, timeSlot'
      })
    }

    // Get waitlist entry
    const waitlistEntry = await getWaitlistEntryById(waitlistId)
    if (!waitlistEntry) {
      return res.status(404).json({
        message: 'Waitlist entry not found'
      })
    }

    if (waitlistEntry.waitlistStatus !== WaitlistStatus.NOTIFIED) {
      return res.status(400).json({
        message: 'Waitlist entry must be in Notified status to convert'
      })
    }

    // Create booking with waitlist reference
    const bookingParams = {
      service_provider_id: waitlistEntry.serviceProviderId.toString(),
      business_location_id: waitlistEntry.businessLocationId.toString(),
      user_id: waitlistEntry.userId.toString(),
      date,
      time_slot: timeSlot,
      service: waitlistEntry.services || [],
      total_duration: waitlistEntry.totalDuration,
      additional_duration: 0,
      total_amount: totalAmount,
      remaining_amount: remainingAmount,
      sub_total: subTotal,
      payment_from: paymentFrom,
      tax,
      wallet_amount: walletAmount,
      promocode,
      product: waitlistEntry.products || [],
      description,
      discount,
      waitlistId: waitlistEntry.id // Link booking to waitlist
    }

    const booking = await createBooking(bookingParams)

    if (!booking) {
      return res.status(500).json({
        message: 'Failed to create booking from waitlist'
      })
    }

    // Update waitlist status to converted
    await updateWaitlistEntry(waitlistId, {
      waitlistStatus: WaitlistStatus.CONVERTED,
      convertedAt: moment().format('YYYY-MM-DD HH:mm:ss')
    })

    res.json({
      message: 'Waitlist successfully converted to booking',
      data: {
        booking,
        waitlistId
      }
    })

  } catch (error) {
    logger.error({ message: 'Failed to convert waitlist to booking', error, meta: { ...req.params, ...req.body } })
    res.status(500).json({
      message: 'Internal server error'
    })
  }
}

/**
 * Remove from waitlist (cancel)
 */
async function removeFromWaitlist(req: Request, res: Response) {
  try {
    const { waitlistId } = req.params
    const { user_type, user_id } = req

    if (!waitlistId) {
      return res.status(400).json({
        message: 'Waitlist ID is required'
      })
    }

    // Get waitlist entry to verify ownership
    const waitlistEntry = await getWaitlistEntryById(waitlistId)
    if (!waitlistEntry) {
      return res.status(404).json({
        message: 'Waitlist entry not found'
      })
    }

    // Check if user has permission to delete this waitlist entry
    if (user_type === USER_TYPE.USER && waitlistEntry.userId !== Number(user_id)) {
      return res.status(403).json({
        message: 'You can only remove your own waitlist entries'
      })
    }

    if (user_type === USER_TYPE.SERVICE_PROVIDER && waitlistEntry.serviceProviderId !== Number(user_id)) {
      return res.status(403).json({
        message: 'You can only remove waitlist entries for your services'
      })
    }

    const success = await deleteWaitlistEntry(waitlistId)

    if (!success) {
      return res.status(500).json({
        message: 'Failed to remove waitlist entry'
      })
    }

    res.json({
      message: 'Successfully removed from waitlist'
    })

  } catch (error) {
    logger.error({ message: 'Failed to remove from waitlist', error, meta: req.params })
    res.status(500).json({
      message: 'Internal server error'
    })
  }
}

/**
 * Get waitlist entries for a service provider
 */
async function getProviderWaitlistEntries(req: Request, res: Response) {
  try {
    const { serviceProviderId } = req.params
    const { status, businessLocationId, date, page = 1, limit = 20 } = req.query
    const { user_type, user_id } = req

    if (!serviceProviderId) {
      return res.status(400).json({
        message: 'Service Provider ID is required'
      })
    }

    // Check if user has permission to view this provider's waitlist
    if (user_type === USER_TYPE.SERVICE_PROVIDER && Number(serviceProviderId) !== Number(user_id)) {
      return res.status(403).json({
        message: 'You can only view your own waitlist entries'
      })
    }

    const filters: any = {
      serviceProviderId: Number(serviceProviderId),
      isDeleted: false,
      limit: Number(limit),
      offset: (Number(page) - 1) * Number(limit)
    }

    if (status) {
      filters.waitlistStatus = status as WaitlistStatus
    }

    if (businessLocationId) {
      filters.businessLocationId = Number(businessLocationId)
    }

    if (date) {
      filters.preferredDate = date
    }

    const waitlistEntries = await getWaitlistEntries(filters)
    const waitlistWithDetails = await getWaitlistEntriesWithDetails(waitlistEntries)

    res.json({
      message: 'Provider waitlist entries retrieved successfully',
      data: waitlistWithDetails,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: waitlistWithDetails.length
      }
    })

  } catch (error) {
    logger.error({ message: 'Failed to get provider waitlist entries', error, meta: req.params })
    res.status(500).json({
      message: 'Internal server error'
    })
  }
}

export default WaitlistRouter
