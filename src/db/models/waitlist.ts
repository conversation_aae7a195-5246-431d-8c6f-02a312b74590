import { sequelize } from '@/src/db'
import { DataTypes } from 'sequelize'

export const AppointmentWaitlist = sequelize.define(
  'tbl_appointment_waitlist',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    serviceProviderId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'service_provider_id',
    },
    businessLocationId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'business_location_id',
    },
    customerId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'customer_id',
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'user_id',
    },
    customerName: {
      type: DataTypes.STRING(256),
      allowNull: false,
      field: 'customer_name',
    },
    preferredDate: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      field: 'preferred_date',
    },
    preferredTimeStart: {
      type: DataTypes.STRING(5),
      allowNull: true,
      field: 'preferred_time_start',
    },
    preferredTimeEnd: {
      type: DataTypes.STRING(5),
      allowNull: true,
      field: 'preferred_time_end',
    },
    totalDuration: {
      type: DataTypes.DOUBLE(8, 2),
      allowNull: false,
      field: 'total_duration',
    },
    services: {
      type: DataTypes.JSON,
      allowNull: false,
    },
    products: {
      type: DataTypes.JSON,
      allowNull: true,
    },
    waitlistStatus: {
      type: DataTypes.ENUM('Active', 'Notified', 'Converted', 'Expired', 'Cancelled'),
      allowNull: false,
      defaultValue: 'Active',
      field: 'waitlist_status',
    },
    notificationSentAt: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'notification_sent_at',
    },
    expiresAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'expires_at',
    },
    priorityScore: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      field: 'priority_score',
    },
    convertedBookingId: {
      type: DataTypes.STRING(128),
      allowNull: true,
      field: 'converted_booking_id',
    },
    convertedAt: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'converted_at',
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      field: 'created_at',
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      field: 'updated_at',
    },
    isDeleted: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      field: 'is_deleted',
    },
  },
  {
    indexes: [
      {
        fields: ['serviceProviderId', 'businessLocationId', 'preferredDate'],
        name: 'idx_sp_location_date',
      },
      {
        fields: ['userId', 'waitlistStatus'],
        name: 'idx_user_status',
      },
      {
        fields: ['waitlistStatus', 'expiresAt'],
        name: 'idx_status_expires',
      },
      {
        fields: ['convertedBookingId'],
        name: 'idx_converted_booking',
      },
    ],
  }
)
