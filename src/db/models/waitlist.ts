import { sequelize } from '@/src/db'
import { DataTypes } from 'sequelize'

export const AppointmentWaitlist = sequelize.define(
  'tbl_appointment_waitlist',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    serviceProviderId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    businessLocationId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    customerId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    preferredDate: {
      type: DataTypes.DATEONLY,
      allowNull: false,
    },
    preferredTimeStart: {
      type: DataTypes.STRING(5),
      allowNull: true,
    },
    preferredTimeEnd: {
      type: DataTypes.STRING(5),
      allowNull: true,
    },
    totalDuration: {
      type: DataTypes.DOUBLE(8, 2),
      allowNull: false,
    },
    waitlistStatus: {
      type: DataTypes.ENUM('Active', 'Notified', 'Converted', 'Expired', 'Cancelled'),
      allowNull: false,
      defaultValue: 'Active',
    },
    notificationSentAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    expiresAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    priorityScore: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    isDeleted: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
  },
  {
    indexes: [
      {
        fields: ['serviceProviderId', 'businessLocationId', 'preferredDate'],
        name: 'idx_sp_location_date',
      },
      {
        fields: ['userId', 'waitlistStatus'],
        name: 'idx_user_status',
      },
      {
        fields: ['waitlistStatus', 'expiresAt'],
        name: 'idx_status_expires',
      },
    ],
  }
)

export const WaitlistService = sequelize.define(
  'tbl_waitlist_services',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    waitlistId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: AppointmentWaitlist,
        key: 'id',
      },
    },
    serviceId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    duration: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    isDeleted: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
  }
)

export const WaitlistProduct = sequelize.define(
  'tbl_waitlist_products',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    waitlistId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: AppointmentWaitlist,
        key: 'id',
      },
    },
    productId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    subproductId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
    },
    quantity: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    isDeleted: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
  }
)

// Define associations
AppointmentWaitlist.hasMany(WaitlistService, {
  foreignKey: 'waitlistId',
  as: 'services',
})

AppointmentWaitlist.hasMany(WaitlistProduct, {
  foreignKey: 'waitlistId',
  as: 'products',
})

WaitlistService.belongsTo(AppointmentWaitlist, {
  foreignKey: 'waitlistId',
  as: 'waitlist',
})

WaitlistProduct.belongsTo(AppointmentWaitlist, {
  foreignKey: 'waitlistId',
  as: 'waitlist',
})
