import { named_db_con } from '@/src/db'
import { logger } from '@/src/utils'
import { 
  IWaitlistEntry, 
  IWaitlistEntryWithDetails,
  IWaitlistService,
  IWaitlistServiceWithDetails,
  IWaitlistProduct,
  IWaitlistProductWithDetails
} from './types'

/**
 * Populate service details for waitlist services
 */
export async function populateServiceDetails(
  waitlistServices: IWaitlistService[]
): Promise<IWaitlistServiceWithDetails[]> {
  if (!waitlistServices?.length) return []

  try {
    const serviceIds = waitlistServices.map(ws => ws.serviceId)
    
    const [serviceRows] = await named_db_con.query(`
      SELECT 
        id,
        service_name as serviceName,
        price as servicePrice,
        duration as serviceDuration,
        description as serviceDescription,
        image as serviceImage,
        is_deleted as isServiceDeleted
      FROM tbl_service 
      WHERE id IN (:serviceIds)
    `, { serviceIds })

    const serviceMap = new Map()
    ;(serviceRows as any[]).forEach(service => {
      serviceMap.set(service.id, service)
    })

    return waitlistServices.map(ws => {
      const serviceDetails = serviceMap.get(ws.serviceId) || {}
      return {
        ...ws,
        serviceName: serviceDetails.serviceName,
        servicePrice: serviceDetails.servicePrice,
        serviceDuration: serviceDetails.serviceDuration,
        serviceDescription: serviceDetails.serviceDescription,
        serviceImage: serviceDetails.serviceImage,
      }
    })
  } catch (error) {
    logger.error({ message: 'Failed to populate service details', error, meta: { waitlistServices } })
    return waitlistServices.map(ws => ({ ...ws }))
  }
}

/**
 * Populate product details for waitlist products
 */
export async function populateProductDetails(
  waitlistProducts: IWaitlistProduct[]
): Promise<IWaitlistProductWithDetails[]> {
  if (!waitlistProducts?.length) return []

  try {
    const productIds = waitlistProducts.map(wp => wp.productId)
    const variantIds = waitlistProducts.map(wp => wp.subproductId).filter(id => id > 0)
    
    // Get product details
    const [productRows] = await named_db_con.query(`
      SELECT 
        id,
        product_name as productName,
        price as productPrice,
        description as productDescription,
        image as productImage,
        is_deleted as isProductDeleted
      FROM product 
      WHERE id IN (:productIds)
    `, { productIds })

    const productMap = new Map()
    ;(productRows as any[]).forEach(product => {
      productMap.set(product.id, product)
    })

    // Get variant details if any
    let variantMap = new Map()
    if (variantIds.length > 0) {
      const [variantRows] = await named_db_con.query(`
        SELECT 
          id,
          product_id as productId,
          subproduct_name as variantName,
          price as variantPrice,
          is_deleted as isVariantDeleted
        FROM tbl_subproduct 
        WHERE id IN (:variantIds)
      `, { variantIds })

      ;(variantRows as any[]).forEach(variant => {
        variantMap.set(variant.id, variant)
      })
    }

    return waitlistProducts.map(wp => {
      const productDetails = productMap.get(wp.productId) || {}
      const variantDetails = wp.subproductId > 0 ? variantMap.get(wp.subproductId) || {} : {}
      
      return {
        ...wp,
        productName: productDetails.productName,
        productPrice: productDetails.productPrice,
        productDescription: productDetails.productDescription,
        productImage: productDetails.productImage,
        variantName: variantDetails.variantName,
        variantPrice: variantDetails.variantPrice,
      }
    })
  } catch (error) {
    logger.error({ message: 'Failed to populate product details', error, meta: { waitlistProducts } })
    return waitlistProducts.map(wp => ({ ...wp }))
  }
}

/**
 * Populate customer details for waitlist entry
 */
export async function populateCustomerDetails(
  waitlistEntry: IWaitlistEntry
): Promise<Partial<IWaitlistEntryWithDetails>> {
  try {
    const [customerRows] = await named_db_con.query(`
      SELECT 
        first_name,
        last_name,
        email,
        phone
      FROM tbl_user 
      WHERE id = :userId
    `, { userId: waitlistEntry.userId })

    const customer = (customerRows as any[])[0]
    if (!customer) return {}

    return {
      customerName: `${customer.first_name} ${customer.last_name}`.trim(),
      customerEmail: customer.email,
      customerPhone: customer.phone,
    }
  } catch (error) {
    logger.error({ message: 'Failed to populate customer details', error, meta: { waitlistEntry } })
    return {}
  }
}

/**
 * Populate service provider and business location details
 */
export async function populateProviderDetails(
  waitlistEntry: IWaitlistEntry
): Promise<Partial<IWaitlistEntryWithDetails>> {
  try {
    const [providerRows] = await named_db_con.query(`
      SELECT 
        sp.first_name as sp_first_name,
        sp.last_name as sp_last_name,
        bl.business_name as business_location_name
      FROM tbl_service_provider sp
      LEFT JOIN tbl_business_location bl ON bl.id = :businessLocationId
      WHERE sp.id = :serviceProviderId
    `, { 
      serviceProviderId: waitlistEntry.serviceProviderId,
      businessLocationId: waitlistEntry.businessLocationId 
    })

    const provider = (providerRows as any[])[0]
    if (!provider) return {}

    return {
      serviceProviderName: `${provider.sp_first_name} ${provider.sp_last_name}`.trim(),
      businessLocationName: provider.business_location_name,
    }
  } catch (error) {
    logger.error({ message: 'Failed to populate provider details', error, meta: { waitlistEntry } })
    return {}
  }
}

/**
 * Get waitlist entry with all details populated
 */
export async function getWaitlistEntryWithDetails(
  waitlistId: string
): Promise<IWaitlistEntryWithDetails | null> {
  try {
    // First get the basic waitlist entry (this will use existing CRUD function)
    const { getWaitlistEntryById } = await import('./waitlistCrud')
    const waitlistEntry = await getWaitlistEntryById(waitlistId)
    
    if (!waitlistEntry) return null

    // Populate all details
    const [
      servicesWithDetails,
      productsWithDetails,
      customerDetails,
      providerDetails
    ] = await Promise.all([
      populateServiceDetails(waitlistEntry.services || []),
      populateProductDetails(waitlistEntry.products || []),
      populateCustomerDetails(waitlistEntry),
      populateProviderDetails(waitlistEntry)
    ])

    return {
      ...waitlistEntry,
      services: servicesWithDetails,
      products: productsWithDetails,
      ...customerDetails,
      ...providerDetails,
    }
  } catch (error) {
    logger.error({ message: 'Failed to get waitlist entry with details', error, meta: { waitlistId } })
    return null
  }
}

/**
 * Get multiple waitlist entries with all details populated
 */
export async function getWaitlistEntriesWithDetails(
  waitlistEntries: IWaitlistEntry[]
): Promise<IWaitlistEntryWithDetails[]> {
  if (!waitlistEntries?.length) return []

  try {
    return await Promise.all(
      waitlistEntries.map(async (entry) => {
        const [
          servicesWithDetails,
          productsWithDetails,
          customerDetails,
          providerDetails
        ] = await Promise.all([
          populateServiceDetails(entry.services || []),
          populateProductDetails(entry.products || []),
          populateCustomerDetails(entry),
          populateProviderDetails(entry)
        ])

        return {
          ...entry,
          services: servicesWithDetails,
          products: productsWithDetails,
          ...customerDetails,
          ...providerDetails,
        }
      })
    )
  } catch (error) {
    logger.error({ message: 'Failed to get waitlist entries with details', error, meta: { waitlistEntries } })
    return waitlistEntries.map(entry => ({ ...entry }))
  }
}
