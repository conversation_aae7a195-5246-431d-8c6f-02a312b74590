import { named_db_con } from '@/src/db'
import { Global } from '@/src/constants'
import { logger } from '@/src/utils'
import {
  IWaitlistEntry,
  IWaitlistEntryWithDetails,
  IWaitlistService,
  IWaitlistServiceWithDetails,
  IWaitlistProduct,
  IWaitlistProductWithDetails
} from './types'

/**
 * Populate service details for waitlist services
 */
export async function populateServiceDetails(
  waitlistServices: IWaitlistService[]
): Promise<IWaitlistServiceWithDetails[]> {
  if (!waitlistServices?.length) return []

  try {
    const serviceIds = waitlistServices.map(ws => ws.serviceId)
    
    const [serviceRows] = await named_db_con.query(`
      SELECT
        id,
        service_name as name,
        price,
        duration,
        description,
        CONCAT(:imgPrefix, service_image) as image,
        is_deleted as isServiceDeleted
      FROM tbl_service
      WHERE id IN (:serviceIds)
    `, {
      serviceIds,
      imgPrefix: `${Global.S3_BUCKET_ROOT}${Global.PRODUCT_IMAGE}`
    })

    const serviceMap = new Map()
    ;(serviceRows as any[]).forEach(service => {
      serviceMap.set(service.id, service)
    })

    return waitlistServices.map(ws => {
      const serviceDetails = serviceMap.get(ws.serviceId) || {}
      return {
        ...ws,
        ...serviceDetails,
      }
    })
  } catch (error) {
    logger.error({ message: 'Failed to populate service details', error, meta: { waitlistServices } })
    return waitlistServices.map(ws => ({ ...ws }))
  }
}

/**
 * Populate product details for waitlist products
 */
export async function populateProductDetails(
  waitlistProducts: IWaitlistProduct[]
): Promise<IWaitlistProductWithDetails[]> {
  if (!waitlistProducts?.length) return []

  try {
    const productVariantIds = waitlistProducts.map(wp => wp.productVariantId)

    // Get product and variant details in one query
    const [productVariantRows] = await named_db_con.query(`
      SELECT
        pv.id as productVariantId,
        p.id as productId,
        p.name,
        p.description,
        p.is_deleted as isProductDeleted,
        pv.price,
        pv.size,
        pv.quantity as availableQuantity,
        pv.is_deleted as isVariantDeleted,
        mu.shortname as unit,
        mu.longname as unitLong,
        (
          SELECT CONCAT(:imgPrefix, g.image)
          FROM product_gallery g
          WHERE g.product_id = p.id
          AND NOT g.is_deleted
          ORDER BY g.id
          LIMIT 1
        ) AS image
      FROM product_variant pv
      JOIN product p ON p.id = pv.product_id
      LEFT JOIN measure_unit mu ON mu.id = pv.measure_unit_id
      WHERE pv.id IN (:productVariantIds)
    `, {
      productVariantIds,
      imgPrefix: `${Global.S3_BUCKET_ROOT}${Global.PRODUCT_IMAGE}`
    })

    const variantMap = new Map()
    ;(productVariantRows as any[]).forEach(row => {
      variantMap.set(row.productVariantId, row)
    })

    return waitlistProducts.map(wp => {
      const details = variantMap.get(wp.productVariantId) || {}
      return {
        ...wp,
        ...details,
      }
    })
  } catch (error) {
    logger.error({ message: 'Failed to populate product details', error, meta: { waitlistProducts } })
    return waitlistProducts.map(wp => ({ ...wp }))
  }
}

/**
 * Populate customer details for waitlist entry
 */
export async function populateCustomerDetails(
  waitlistEntry: IWaitlistEntry
): Promise<Partial<IWaitlistEntryWithDetails>> {
  try {
    const [customerRows] = await named_db_con.query(`
      SELECT 
        first_name,
        last_name,
        email,
        phone
      FROM tbl_user 
      WHERE id = :userId
    `, { userId: waitlistEntry.userId })

    const customer = (customerRows as any[])[0]
    if (!customer) return {}

    return {
      customerName: `${customer.first_name} ${customer.last_name}`.trim(),
      customerEmail: customer.email,
      customerPhone: customer.phone,
    }
  } catch (error) {
    logger.error({ message: 'Failed to populate customer details', error, meta: { waitlistEntry } })
    return {}
  }
}

/**
 * Populate service provider and business location details
 */
export async function populateProviderDetails(
  waitlistEntry: IWaitlistEntry
): Promise<Partial<IWaitlistEntryWithDetails>> {
  try {
    const [providerRows] = await named_db_con.query(`
      SELECT 
        sp.first_name as sp_first_name,
        sp.last_name as sp_last_name,
        bl.business_name as business_location_name
      FROM tbl_service_provider sp
      LEFT JOIN tbl_business_location bl ON bl.id = :businessLocationId
      WHERE sp.id = :serviceProviderId
    `, { 
      serviceProviderId: waitlistEntry.serviceProviderId,
      businessLocationId: waitlistEntry.businessLocationId 
    })

    const provider = (providerRows as any[])[0]
    if (!provider) return {}

    return {
      serviceProviderName: `${provider.sp_first_name} ${provider.sp_last_name}`.trim(),
      businessLocationName: provider.business_location_name,
    }
  } catch (error) {
    logger.error({ message: 'Failed to populate provider details', error, meta: { waitlistEntry } })
    return {}
  }
}

/**
 * Get waitlist entry with all details populated
 */
export async function getWaitlistEntryWithDetails(
  waitlistId: string
): Promise<IWaitlistEntryWithDetails | null> {
  try {
    // First get the basic waitlist entry (this will use existing CRUD function)
    const { getWaitlistEntryById } = await import('./waitlistCrud')
    const waitlistEntry = await getWaitlistEntryById(waitlistId)
    
    if (!waitlistEntry) return null

    // Populate all details
    const [
      servicesWithDetails,
      productsWithDetails,
      customerDetails,
      providerDetails
    ] = await Promise.all([
      populateServiceDetails(waitlistEntry.services || []),
      populateProductDetails(waitlistEntry.products || []),
      populateCustomerDetails(waitlistEntry),
      populateProviderDetails(waitlistEntry)
    ])

    return {
      ...waitlistEntry,
      services: servicesWithDetails,
      products: productsWithDetails,
      ...customerDetails,
      ...providerDetails,
    }
  } catch (error) {
    logger.error({ message: 'Failed to get waitlist entry with details', error, meta: { waitlistId } })
    return null
  }
}

/**
 * Get multiple waitlist entries with all details populated
 */
export async function getWaitlistEntriesWithDetails(
  waitlistEntries: IWaitlistEntry[]
): Promise<IWaitlistEntryWithDetails[]> {
  if (!waitlistEntries?.length) return []

  try {
    return await Promise.all(
      waitlistEntries.map(async (entry) => {
        const [
          servicesWithDetails,
          productsWithDetails,
          customerDetails,
          providerDetails
        ] = await Promise.all([
          populateServiceDetails(entry.services || []),
          populateProductDetails(entry.products || []),
          populateCustomerDetails(entry),
          populateProviderDetails(entry)
        ])

        return {
          ...entry,
          services: servicesWithDetails,
          products: productsWithDetails,
          ...customerDetails,
          ...providerDetails,
        }
      })
    )
  } catch (error) {
    logger.error({ message: 'Failed to get waitlist entries with details', error, meta: { waitlistEntries } })
    return waitlistEntries.map(entry => ({ ...entry }))
  }
}
