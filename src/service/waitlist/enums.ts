export enum WaitlistStatus {
  ACTIVE = 'Active',
  NOTIFIED = 'Notified',
  CONVERTED = 'Converted',
  EXPIRED = 'Expired',
  CANCELLED = 'Cancelled'
}

export const WAITLIST_STATUS = {
  ACTIVE: 'Active' as const,
  NOTIFIED: 'Notified' as const,
  CONVERTED: 'Converted' as const,
  EXPIRED: 'Expired' as const,
  CANCELLED: 'Cancelled' as const,
} as const

export type WaitlistStatusType = typeof WAITLIST_STATUS[keyof typeof WAITLIST_STATUS]
