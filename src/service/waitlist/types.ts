export interface IWaitlistService {
  id?: string
  waitlistId?: string
  serviceId: number
  duration: number
  price: number
  createdAt?: string
  updatedAt?: string
  isDeleted?: boolean
}

export interface IWaitlistProduct {
  id?: string
  waitlistId?: string
  productId: number
  subproductId: number
  quantity: number
  price: number
  createdAt?: string
  updatedAt?: string
  isDeleted?: boolean
}

export interface IWaitlistEntry {
  id?: string
  serviceProviderId: number
  businessLocationId: number
  customerId: number
  userId: number
  preferredDate: string
  preferredTimeStart?: string
  preferredTimeEnd?: string
  totalDuration: number
  services?: IWaitlistService[]
  products?: IWaitlistProduct[]
  waitlistStatus?: 'Active' | 'Notified' | 'Converted' | 'Expired' | 'Cancelled'
  notificationSentAt?: string
  expiresAt: string
  priorityScore?: number
  convertedBookingId?: string
  convertedAt?: string
  createdAt?: string
  updatedAt?: string
  isDeleted?: boolean
}

export interface IJoinWaitlistParams {
  serviceProviderId: number
  businessLocationId: number
  customerId: number
  userId: number
  preferredDate: string
  preferredTimeStart?: string
  preferredTimeEnd?: string
  totalDuration: number
  services: IWaitlistService[]
  products?: IWaitlistProduct[]
  expirationDays?: number // Default 30 days
}

export interface IConvertWaitlistParams {
  waitlistId: string
  date: string
  timeSlot: string
  totalAmount: string
  remainingAmount: string
  subTotal?: string
  paymentFrom?: string
  tax?: number
  walletAmount?: number
  promocode?: string
  discount?: string
  description?: string
}

export interface IWaitlistNotificationData {
  waitlistEntry: IWaitlistEntry
  availableSlot: {
    date: string
    timeSlot: string
    serviceProviderName: string
    businessLocationName: string
  }
  expirationTime: string // How long they have to respond
}

export enum WaitlistStatus {
  ACTIVE = 'Active',
  NOTIFIED = 'Notified',
  CONVERTED = 'Converted',
  EXPIRED = 'Expired',
  CANCELLED = 'Cancelled'
}

export interface IWaitlistFilters {
  serviceProviderId?: number
  businessLocationId?: number
  userId?: number
  waitlistStatus?: WaitlistStatus
  preferredDate?: string
  isDeleted?: boolean
  limit?: number
  offset?: number
}
