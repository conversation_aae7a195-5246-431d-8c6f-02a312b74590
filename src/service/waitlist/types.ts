export interface IWaitlistService {
  id?: string
  waitlistId?: string
  serviceId: number
  createdAt?: string
  updatedAt?: string
  isDeleted?: boolean
}

export interface IWaitlistServiceWithDetails extends IWaitlistService {
  name?: string
  price?: number
  duration?: number
  description?: string
  image?: string
}

export interface IWaitlistProduct {
  id?: string
  waitlistId?: string
  productVariantId: number
  quantity: number
  createdAt?: string
  updatedAt?: string
  isDeleted?: boolean
}

export interface IWaitlistProductWithDetails extends IWaitlistProduct {
  productId?: number
  name?: string
  description?: string
  image?: string
  price?: number
  size?: number
  availableQuantity?: number
  unit?: string
  unitLong?: string
}

export interface IWaitlistEntry {
  id?: string
  serviceProviderId: number
  businessLocationId: number
  userId: number
  preferredDate: string
  preferredTimeStart?: string
  preferredTimeEnd?: string
  totalDuration: number
  services?: IWaitlistService[]
  products?: IWaitlistProduct[]
  waitlistStatus?: 'Active' | 'Notified' | 'Converted' | 'Expired' | 'Cancelled'
  notificationSentAt?: string
  expiresAt: string
  priorityScore?: number
  convertedAt?: string
  createdAt?: string
  updatedAt?: string
  isDeleted?: boolean
}

export interface IWaitlistEntryWithDetails extends IWaitlistEntry {
  services?: IWaitlistServiceWithDetails[]
  products?: IWaitlistProductWithDetails[]
  customerName?: string
  customerEmail?: string
  customerPhone?: string
  serviceProviderName?: string
  businessLocationName?: string
}

export interface IJoinWaitlistParams {
  serviceProviderId: number
  businessLocationId: number
  userId: number
  preferredDate: string
  preferredTimeStart?: string
  preferredTimeEnd?: string
  totalDuration: number
  services: IWaitlistService[]
  products?: IWaitlistProduct[]
  expirationDays?: number
}

export interface IConvertWaitlistParams {
  waitlistId: string
  date: string
  timeSlot: string
  totalAmount: string
  remainingAmount: string
  subTotal?: string
  paymentFrom?: string
  tax?: number
  walletAmount?: number
  promocode?: string
  discount?: string
  description?: string
}

export interface IWaitlistNotificationData {
  waitlistEntry: IWaitlistEntry
  availableSlot: {
    date: string
    timeSlot: string
    serviceProviderName: string
    businessLocationName: string
  }
  expirationTime: string // How long they have to respond
}

import { WaitlistStatus } from './enums'

export interface IWaitlistFilters {
  serviceProviderId?: number
  businessLocationId?: number
  userId?: number
  waitlistStatus?: WaitlistStatus
  preferredDate?: string
  isDeleted?: boolean
  limit?: number
  offset?: number
}
