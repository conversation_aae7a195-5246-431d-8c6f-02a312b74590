// Waitlist service record (stored in database)
export interface IWaitlistService {
  id?: string
  waitlistId?: string
  serviceId: number
  createdAt?: string
  updatedAt?: string
  isDeleted?: boolean
}

// Service details populated when querying (from service table)
export interface IWaitlistServiceWithDetails extends IWaitlistService {
  name?: string
  price?: number
  duration?: number
  description?: string
  image?: string
}

// Waitlist product record (stored in database)
export interface IWaitlistProduct {
  id?: string
  waitlistId?: string
  productId: number
  subproductId: number
  quantity: number
  createdAt?: string
  updatedAt?: string
  isDeleted?: boolean
}

// Product details populated when querying (from product table)
export interface IWaitlistProductWithDetails extends IWaitlistProduct {
  productName?: string
  productPrice?: number
  productDescription?: string
  productImage?: string
  variantName?: string
  variantPrice?: number
}

// Basic waitlist entry (stored in database)
export interface IWaitlistEntry {
  id?: string
  serviceProviderId: number
  businessLocationId: number
  customerId: number
  userId: number
  preferredDate: string
  preferredTimeStart?: string
  preferredTimeEnd?: string
  totalDuration: number
  services?: IWaitlistService[]
  products?: IWaitlistProduct[]
  waitlistStatus?: 'Active' | 'Notified' | 'Converted' | 'Expired' | 'Cancelled'
  notificationSentAt?: string
  expiresAt: string
  priorityScore?: number
  createdAt?: string
  updatedAt?: string
  isDeleted?: boolean
}

// Waitlist entry with full details (for API responses)
export interface IWaitlistEntryWithDetails extends IWaitlistEntry {
  services?: IWaitlistServiceWithDetails[]
  products?: IWaitlistProductWithDetails[]
  // Customer details populated from user table
  customerName?: string
  customerEmail?: string
  customerPhone?: string
  // Service provider details
  serviceProviderName?: string
  businessLocationName?: string
}

export interface IJoinWaitlistParams {
  serviceProviderId: number
  businessLocationId: number
  customerId: number
  userId: number
  preferredDate: string
  preferredTimeStart?: string
  preferredTimeEnd?: string
  totalDuration: number
  services: IWaitlistService[]
  products?: IWaitlistProduct[]
  expirationDays?: number // Default 30 days
}

export interface IConvertWaitlistParams {
  waitlistId: string
  date: string
  timeSlot: string
  totalAmount: string
  remainingAmount: string
  subTotal?: string
  paymentFrom?: string
  tax?: number
  walletAmount?: number
  promocode?: string
  discount?: string
  description?: string
}

export interface IWaitlistNotificationData {
  waitlistEntry: IWaitlistEntry
  availableSlot: {
    date: string
    timeSlot: string
    serviceProviderName: string
    businessLocationName: string
  }
  expirationTime: string // How long they have to respond
}

import { WaitlistStatus } from './enums'

export interface IWaitlistFilters {
  serviceProviderId?: number
  businessLocationId?: number
  userId?: number
  waitlistStatus?: WaitlistStatus
  preferredDate?: string
  isDeleted?: boolean
  limit?: number
  offset?: number
}
