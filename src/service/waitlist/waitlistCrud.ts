import moment from 'moment'
import { Op, Transaction } from 'sequelize'
import { AppointmentWaitlist, WaitlistService, WaitlistProduct } from '@/src/db/models'
import { sequelize } from '@/src/db'
import { logger } from '@/src/utils'
import {
  IWaitlistEntry,
  IJoinWaitlistParams,
  IWaitlistFilters
} from './types'
import { WaitlistStatus } from './enums'

/**
 * Create a new waitlist entry with services and products
 */
export async function createWaitlistEntry(params: IJoinWaitlistParams): Promise<IWaitlistEntry | null> {
  const transaction = await sequelize.transaction()

  try {
    const expirationDays = params.expirationDays || 30
    const expiresAt = moment().add(expirationDays, 'days').format('YYYY-MM-DD HH:mm:ss')

    // Create main waitlist entry
    const waitlistEntry = await AppointmentWaitlist.create({
      serviceProviderId: params.serviceProviderId,
      businessLocationId: params.businessLocationId,
      customerId: params.customerId,
      userId: params.userId,
      preferredDate: params.preferredDate,
      preferredTimeStart: params.preferredTimeStart,
      preferredTimeEnd: params.preferredTimeEnd,
      totalDuration: params.totalDuration,
      waitlistStatus: WaitlistStatus.ACTIVE,
      expiresAt,
      priorityScore: 0,
      isDeleted: false,
    }, { transaction })

    const waitlistId = waitlistEntry.get('id') as string

    // Create services
    if (params.services?.length) {
      await WaitlistService.bulkCreate(
        params.services.map(service => ({
          waitlistId,
          serviceId: service.serviceId,
          duration: service.duration,
          isDeleted: false,
        })),
        { transaction }
      )
    }

    // Create products if any
    if (params.products?.length) {
      await WaitlistProduct.bulkCreate(
        params.products.map(product => ({
          waitlistId,
          productId: product.productId,
          subproductId: product.subproductId,
          quantity: product.quantity,
          isDeleted: false,
        })),
        { transaction }
      )
    }

    await transaction.commit()

    // Return the complete waitlist entry with associations
    return await getWaitlistEntryById(waitlistId)
  } catch (error) {
    await transaction.rollback()
    logger.error({ message: 'Failed to create waitlist entry', error, meta: params })
    return null
  }
}

/**
 * Get waitlist entry by ID with services and products
 */
export async function getWaitlistEntryById(id: string): Promise<IWaitlistEntry | null> {
  try {
    const waitlistEntry = await AppointmentWaitlist.findOne({
      where: {
        id,
        isDeleted: false,
      },
      include: [
        {
          model: WaitlistService,
          as: 'services',
          required: false,
          // Don't filter by isDeleted - return all services but preserve isDeleted info
        },
        {
          model: WaitlistProduct,
          as: 'products',
          required: false,
          // Don't filter by isDeleted - return all products but preserve isDeleted info
        },
      ],
    })

    return waitlistEntry ? (waitlistEntry.toJSON() as IWaitlistEntry) : null
  } catch (error) {
    logger.error({ message: 'Failed to get waitlist entry by ID', error, meta: { id } })
    return null
  }
}

/**
 * Get waitlist entries with filters and associations
 */
export async function getWaitlistEntries(filters: IWaitlistFilters = {}): Promise<IWaitlistEntry[]> {
  try {
    const whereClause: any = {
      isDeleted: filters.isDeleted ?? false,
    }

    if (filters.serviceProviderId) {
      whereClause.serviceProviderId = filters.serviceProviderId
    }

    if (filters.businessLocationId) {
      whereClause.businessLocationId = filters.businessLocationId
    }

    if (filters.userId) {
      whereClause.userId = filters.userId
    }

    if (filters.waitlistStatus) {
      whereClause.waitlistStatus = filters.waitlistStatus
    }

    if (filters.preferredDate) {
      whereClause.preferredDate = filters.preferredDate
    }

    const waitlistEntries = await AppointmentWaitlist.findAll({
      where: whereClause,
      include: [
        {
          model: WaitlistService,
          as: 'services',
          required: false,
          // Don't filter by isDeleted - return all services but preserve isDeleted info
        },
        {
          model: WaitlistProduct,
          as: 'products',
          required: false,
          // Don't filter by isDeleted - return all products but preserve isDeleted info
        },
      ],
      order: [['createdAt', 'ASC']], // First-come, first-served
      limit: filters.limit || 100,
      offset: filters.offset || 0,
    })

    return waitlistEntries.map(entry => entry.toJSON() as IWaitlistEntry)
  } catch (error) {
    logger.error({ message: 'Failed to get waitlist entries', error, meta: filters })
    return []
  }
}

/**
 * Update waitlist entry
 */
export async function updateWaitlistEntry(
  id: string, 
  updates: Partial<IWaitlistEntry>
): Promise<boolean> {
  try {
    const [affectedRows] = await AppointmentWaitlist.update(
      {
        ...updates,
        updatedAt: moment().format('YYYY-MM-DD HH:mm:ss'),
      },
      {
        where: {
          id,
          isDeleted: false,
        },
      }
    )

    return affectedRows > 0
  } catch (error) {
    logger.error({ message: 'Failed to update waitlist entry', error, meta: { id, updates } })
    return false
  }
}

/**
 * Soft delete waitlist entry
 */
export async function deleteWaitlistEntry(id: string): Promise<boolean> {
  try {
    const [affectedRows] = await AppointmentWaitlist.update(
      {
        isDeleted: true,
        updatedAt: moment().format('YYYY-MM-DD HH:mm:ss'),
      },
      {
        where: {
          id,
          isDeleted: false,
        },
      }
    )

    return affectedRows > 0
  } catch (error) {
    logger.error({ message: 'Failed to delete waitlist entry', error, meta: { id } })
    return false
  }
}

/**
 * Get active waitlist entries for availability checking
 */
export async function getActiveWaitlistEntries(
  serviceProviderId: number,
  businessLocationId: number,
  date?: string
): Promise<IWaitlistEntry[]> {
  try {
    const whereClause: any = {
      serviceProviderId,
      businessLocationId,
      waitlistStatus: WaitlistStatus.ACTIVE,
      isDeleted: false,
      expiresAt: {
        [Op.gt]: moment().format('YYYY-MM-DD HH:mm:ss'),
      },
    }

    if (date) {
      whereClause.preferredDate = date
    }

    const waitlistEntries = await AppointmentWaitlist.findAll({
      where: whereClause,
      include: [
        {
          model: WaitlistService,
          as: 'services',
          where: { isDeleted: false },
          required: true, // Must have active services for availability checking
        },
        {
          model: WaitlistProduct,
          as: 'products',
          required: false,
          // Don't filter by isDeleted for products - return all but preserve isDeleted info
        },
      ],
      order: [
        ['priorityScore', 'DESC'], // Higher priority first
        ['createdAt', 'ASC'],      // Then first-come, first-served
      ],
    })

    return waitlistEntries.map(entry => entry.toJSON() as IWaitlistEntry)
  } catch (error) {
    logger.error({
      message: 'Failed to get active waitlist entries',
      error,
      meta: { serviceProviderId, businessLocationId, date }
    })
    return []
  }
}

/**
 * Get expired waitlist entries for cleanup
 */
export async function getExpiredWaitlistEntries(): Promise<IWaitlistEntry[]> {
  try {
    const waitlistEntries = await AppointmentWaitlist.findAll({
      where: {
        waitlistStatus: {
          [Op.in]: [WaitlistStatus.ACTIVE, WaitlistStatus.NOTIFIED],
        },
        expiresAt: {
          [Op.lt]: moment().format('YYYY-MM-DD HH:mm:ss'),
        },
        isDeleted: false,
      },
    })

    return waitlistEntries.map(entry => entry.toJSON() as IWaitlistEntry)
  } catch (error) {
    logger.error({ message: 'Failed to get expired waitlist entries', error })
    return []
  }
}

/**
 * Mark waitlist entries as expired
 */
export async function markWaitlistEntriesAsExpired(ids: string[]): Promise<boolean> {
  try {
    if (!ids.length) return true

    const [affectedRows] = await AppointmentWaitlist.update(
      {
        waitlistStatus: WaitlistStatus.EXPIRED,
        updatedAt: moment().format('YYYY-MM-DD HH:mm:ss'),
      },
      {
        where: {
          id: {
            [Op.in]: ids,
          },
          isDeleted: false,
        },
      }
    )

    return affectedRows > 0
  } catch (error) {
    logger.error({ message: 'Failed to mark waitlist entries as expired', error, meta: { ids } })
    return false
  }
}
